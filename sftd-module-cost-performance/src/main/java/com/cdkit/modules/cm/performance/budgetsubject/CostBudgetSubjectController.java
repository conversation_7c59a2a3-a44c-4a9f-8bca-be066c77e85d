package com.cdkit.modules.cm.performance.budgetsubject;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.api.budgetsubject.IBudgetSubjectApi;
import com.cdkit.modules.cm.api.budgetsubject.dto.CostBudgetSubjectDTO;
import com.cdkit.modules.cm.api.budgetsubject.dto.CostBudgetSubjectAddRequest;
import com.cdkit.modules.cm.api.budgetsubject.dto.CostBudgetSubjectEditRequest;
import com.cdkit.modules.cm.application.budgetsubject.BudgetSubjectApplication;
import com.cdkit.modules.cm.domain.budgetsubject.entity.CostBudgetSubjectEntity;
import com.cdkit.modules.cm.application.budgetsubject.converter.CostBudgetSubjectConverter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 预算科目管理控制器
 * <AUTHOR>
 * @date 2025-07-31
 */
@Tag(name = "预算科目管理")
@RestController
@RequestMapping("/cm/costBudgetSubject")
@RequiredArgsConstructor
@Slf4j
public class CostBudgetSubjectController implements IBudgetSubjectApi {

    private final BudgetSubjectApplication budgetSubjectApplication;

    @Override
    public Result<IPage<CostBudgetSubjectDTO>> queryPageList(CostBudgetSubjectDTO queryVO, Integer pageNo, Integer pageSize) {
        log.info("开始分页查询预算科目列表，页码: {}, 每页数量: {}", pageNo, pageSize);
        
        try {
            CostBudgetSubjectEntity buildDomain = CostBudgetSubjectConverter.toEntity(queryVO);
            PageRes<CostBudgetSubjectEntity> queryPageList = budgetSubjectApplication.queryPageList(buildDomain, pageNo, pageSize);

            // 使用 MyBatis Plus 的分页对象
            IPage<CostBudgetSubjectDTO> page = new Page<CostBudgetSubjectDTO>(pageNo, pageSize);
            if (queryPageList != null) {
                page.setCurrent(queryPageList.getCurrent());
                page.setSize(queryPageList.getSize());
                page.setTotal(queryPageList.getTotal());
                page.setRecords(CostBudgetSubjectConverter.toDTOList(queryPageList.getRecords()));
            }

            log.info("分页查询预算科目列表成功，总数: {}", page.getTotal());
            return Result.OK(page);
            
        } catch (Exception e) {
            log.error("分页查询预算科目列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<CostBudgetSubjectDTO> queryById(String id) {
        log.info("开始根据ID查询预算科目详情，ID: {}", id);
        
        try {
            CostBudgetSubjectEntity entity = budgetSubjectApplication.queryById(id);
            if (entity == null) {
                log.warn("预算科目不存在，ID: {}", id);
                return Result.error("预算科目不存在");
            }
            
            CostBudgetSubjectDTO dto = CostBudgetSubjectConverter.toDTO(entity);
            log.info("根据ID查询预算科目详情成功，ID: {}, 科目编码: {}", id, dto.getSubjectCode());
            return Result.OK(dto);
            
        } catch (Exception e) {
            log.error("根据ID查询预算科目详情失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> add(CostBudgetSubjectAddRequest costBudgetSubject) {
        log.info("开始新增预算科目，科目名称: {}", costBudgetSubject != null ? costBudgetSubject.getSubjectName() : "null");
        
        try {
            if (costBudgetSubject == null) {
                return Result.error("预算科目数据不能为空");
            }
            
            CostBudgetSubjectEntity entity = CostBudgetSubjectConverter.toEntity(costBudgetSubject);
            String id = budgetSubjectApplication.add(entity);
            
            log.info("新增预算科目成功，科目名称: {}, ID: {}", costBudgetSubject.getSubjectName(), id);
            return Result.OK("新增成功");
            
        } catch (Exception e) {
            log.error("新增预算科目失败，科目名称: {}", costBudgetSubject != null ? costBudgetSubject.getSubjectName() : "null", e);
            return Result.error("新增失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> edit(CostBudgetSubjectEditRequest costBudgetSubject) {
        log.info("开始编辑预算科目，ID: {}, 科目名称: {}", 
                costBudgetSubject != null ? costBudgetSubject.getId() : "null",
                costBudgetSubject != null ? costBudgetSubject.getSubjectName() : "null");
        
        try {
            if (costBudgetSubject == null) {
                return Result.error("预算科目数据不能为空");
            }
            
            CostBudgetSubjectEntity entity = CostBudgetSubjectConverter.toEntity(costBudgetSubject);
            String id = budgetSubjectApplication.edit(entity);
            
            log.info("编辑预算科目成功，ID: {}, 科目名称: {}", id, costBudgetSubject.getSubjectName());
            return Result.OK("编辑成功");
            
        } catch (Exception e) {
            log.error("编辑预算科目失败，ID: {}, 科目名称: {}",
                    costBudgetSubject != null ? costBudgetSubject.getId() : "null",
                    costBudgetSubject != null ? costBudgetSubject.getSubjectName() : "null", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> delete(String id) {
        log.info("开始删除预算科目，ID: {}", id);

        try {
            if (!StringUtils.hasText(id)) {
                return Result.error("预算科目ID不能为空");
            }

            budgetSubjectApplication.delete(id);

            log.info("删除预算科目成功，ID: {}", id);
            return Result.OK("删除成功");

        } catch (Exception e) {
            log.error("删除预算科目失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> deleteBatch(String ids) {
        log.info("开始批量删除预算科目，IDs: {}", ids);

        try {
            if (!StringUtils.hasText(ids)) {
                return Result.error("预算科目ID列表不能为空");
            }

            List<String> idList = Arrays.asList(ids.split(","));
            budgetSubjectApplication.deleteBatch(idList);

            log.info("批量删除预算科目成功，删除数量: {}", idList.size());
            return Result.OK("批量删除成功");

        } catch (Exception e) {
            log.error("批量删除预算科目失败，IDs: {}", ids, e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<CostBudgetSubjectDTO>> listEnabled() {
        log.info("开始查询所有启用状态的预算科目");

        try {
            List<CostBudgetSubjectEntity> entityList = budgetSubjectApplication.listEnabled();
            List<CostBudgetSubjectDTO> dtoList = CostBudgetSubjectConverter.toDTOList(entityList);

            log.info("查询所有启用状态的预算科目成功，数量: {}", dtoList.size());
            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("查询所有启用状态的预算科目失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
