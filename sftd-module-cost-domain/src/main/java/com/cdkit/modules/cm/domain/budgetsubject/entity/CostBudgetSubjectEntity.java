package com.cdkit.modules.cm.domain.budgetsubject.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 预算科目管理领域实体
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@Accessors(chain = true)
public class CostBudgetSubjectEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    private String id;

    /**预算科目编码*/
    private String subjectCode;

    /**预算科目名称*/
    private String subjectName;

    /**科目释义*/
    private String subjectDescription;

    /**状态(Y-启用/N-停用)*/
    private String subjectStatus;

    /**排序号*/
    private Integer sortOrder;

    /**创建时间*/
    private Date createTime;

    /**创建人*/
    private String createBy;

    /**更新时间*/
    private Date updateTime;

    /**更新人*/
    private String updateBy;

    /**租户ID*/
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/
    private Integer delFlag;

    /**所属部门代码*/
    private String sysOrgCode;

    /**
     * 是否可以删除
     * 启用状态的科目不允许删除
     */
    public boolean canDelete() {
        return !"Y".equals(this.subjectStatus);
    }

    /**
     * 是否启用状态
     */
    public boolean isEnabled() {
        return "Y".equals(this.subjectStatus);
    }

    /**
     * 启用科目
     */
    public void enable() {
        this.subjectStatus = "Y";
    }

    /**
     * 停用科目
     */
    public void disable() {
        this.subjectStatus = "N";
    }
}
