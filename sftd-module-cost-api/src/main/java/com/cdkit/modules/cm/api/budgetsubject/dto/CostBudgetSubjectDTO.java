package com.cdkit.modules.cm.api.budgetsubject.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 预算科目管理DTO
 * @Author: sunhzh
 * @Date: 2025-07-31
 * @Version: V1.0
 */
@Schema(description = "预算科目管理DTO")
@Data
public class CostBudgetSubjectDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;

    /**预算科目编码*/
    @Excel(name = "预算科目编码", width = 15)
    @Schema(description = "预算科目编码")
    private String subjectCode;

    /**预算科目名称*/
    @Excel(name = "预算科目名称", width = 15)
    @Schema(description = "预算科目名称")
    private String subjectName;

    /**科目释义*/
    @Excel(name = "科目释义", width = 15)
    @Schema(description = "科目释义")
    private String subjectDescription;

    /**状态(Y-启用/N-停用)*/
    @Excel(name = "状态(Y-启用/N-停用)", width = 15, dicCode = "cost_y_n")
    @Dict(dicCode = "cost_y_n")
    @Schema(description = "状态(Y-启用/N-停用)")
    private String subjectStatus;

    /**排序号*/
    @Excel(name = "排序号", width = 15)
    @Schema(description = "排序号")
    private Integer sortOrder;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String updateBy;

    /**租户ID*/

    @Schema(description = "租户ID")
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/

    @Schema(description = "删除标识 0:未删除 1:删除")
    private Integer delFlag;

    /**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
