package com.cdkit.modules.cm.application.budgetsubject.converter;

import com.cdkit.modules.cm.api.budgetsubject.dto.CostBudgetSubjectDTO;
import com.cdkit.modules.cm.api.budgetsubject.dto.CostBudgetSubjectAddRequest;
import com.cdkit.modules.cm.api.budgetsubject.dto.CostBudgetSubjectEditRequest;
import com.cdkit.modules.cm.domain.budgetsubject.entity.CostBudgetSubjectEntity;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预算科目管理转换器
 * <AUTHOR>
 * @date 2025-07-31
 */
public class CostBudgetSubjectConverter {

    /**
     * 实体转DTO
     */
    public static CostBudgetSubjectDTO toDTO(CostBudgetSubjectEntity entity) {
        if (entity == null) {
            return null;
        }
        CostBudgetSubjectDTO dto = new CostBudgetSubjectDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * DTO转实体
     */
    public static CostBudgetSubjectEntity toEntity(CostBudgetSubjectDTO dto) {
        if (dto == null) {
            return null;
        }
        CostBudgetSubjectEntity entity = new CostBudgetSubjectEntity();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 新增请求转实体
     */
    public static CostBudgetSubjectEntity toEntity(CostBudgetSubjectAddRequest request) {
        if (request == null) {
            return null;
        }
        CostBudgetSubjectEntity entity = new CostBudgetSubjectEntity();
        BeanUtils.copyProperties(request, entity);
        return entity;
    }

    /**
     * 编辑请求转实体
     */
    public static CostBudgetSubjectEntity toEntity(CostBudgetSubjectEditRequest request) {
        if (request == null) {
            return null;
        }
        CostBudgetSubjectEntity entity = new CostBudgetSubjectEntity();
        BeanUtils.copyProperties(request, entity);
        return entity;
    }

    /**
     * 实体列表转DTO列表
     */
    public static List<CostBudgetSubjectDTO> toDTOList(List<CostBudgetSubjectEntity> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream()
                .map(CostBudgetSubjectConverter::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * DTO列表转实体列表
     */
    public static List<CostBudgetSubjectEntity> toEntityList(List<CostBudgetSubjectDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
                .map(CostBudgetSubjectConverter::toEntity)
                .collect(Collectors.toList());
    }
}
